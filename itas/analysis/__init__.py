"""
Analysis and evaluation tools for ITAS.

This module provides comprehensive tools for analyzing SAE features,
extracting functions, and performing representation engineering.
"""

from .activation_analyzer import ActivationAnalyzer, ActivationStats
from .function_extractor import FunctionExtractor, FunctionExtractionResult
from .representation_engineer import RepresentationEngineer, InterventionResult
from .evaluation import SAEEvaluator, SAEEvaluationResult, InterventionEvaluationResult
from .visualization import SAEVisualizer
from .sae_activation_classifier import (
    SAEActivationClassifier,
    LogisticRegressionClassifier,
    ClassificationResult,
    TrainingConfig,
    create_classifier_from_sae,
    train_conflict_classifier,
)

__all__ = [
    # Main classes
    "ActivationAnalyzer",
    "FunctionExtractor",
    "RepresentationEngineer",
    "SAEEvaluator",
    "SAEVisualizer",
    "SAEActivationClassifier",
    # Result classes
    "ActivationStats",
    "FunctionExtractionResult",
    "InterventionResult",
    "SAEEvaluationResult",
    "InterventionEvaluationResult",
    "ClassificationResult",
    # Classifier components
    "LogisticRegressionClassifier",
    "TrainingConfig",
    # Utility functions
    "create_classifier_from_sae",
    "train_conflict_classifier",
]
